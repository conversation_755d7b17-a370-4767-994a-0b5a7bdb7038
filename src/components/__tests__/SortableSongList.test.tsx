import React from 'react'
import { render, fireEvent, waitFor, act } from '@testing-library/react-native'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { TamaguiProvider } from 'tamagui'
import SortableSongList, { 
  convertMusicSongToRankable, 
  useSortableSongActions,
  RankableSong 
} from '../SortableSongList'
import { useSortableSongStore } from '@/stores/useSortableSongStore'
import config from '../../../tamagui.config'

// Mock dependencies
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy'
  }
}))

jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native')
  return {
    ...RN,
    AccessibilityInfo: {
      isScreenReaderEnabled: jest.fn(() => Promise.resolve(false)),
      addEventListener: jest.fn(() => ({ remove: jest.fn() })),
    },
    Alert: {
      alert: jest.fn(),
    },
  }
})

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TamaguiProvider config={config}>
    <GestureHandlerRootView style={{ flex: 1 }}>
      {children}
    </GestureHandlerRootView>
  </TamaguiProvider>
)

// Mock song data
const mockSong: RankableSong = {
  id: 'test-song-1',
  artworkURL: 'https://example.com/artwork.jpg',
  trackName: 'Test Song',
  artistName: 'Test Artist',
  albumName: 'Test Album',
  audioPreviewURL: 'https://example.com/preview.mp3',
}

const mockMusicSong = {
  id: 'music-song-1',
  attributes: {
    artwork: { url: 'https://example.com/{w}x{h}.jpg', width: 300, height: 300 },
    name: 'Music Song',
    artistName: 'Music Artist',
    previews: [{ url: 'https://example.com/preview.mp3' }],
  },
}

describe('SortableSongList', () => {
  beforeEach(() => {
    // Reset store before each test
    useSortableSongStore.getState().resetToDefaults()
    jest.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders correctly with default props', () => {
      const { getByText, getByLabelText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      expect(getByText('Rank Your Top 10 Songs')).toBeTruthy()
      expect(getByLabelText('Add song to rankings')).toBeTruthy()
      expect(getByLabelText('Clear all rankings')).toBeTruthy()
      expect(getByLabelText('Reset to defaults')).toBeTruthy()
    })

    it('renders with custom title', () => {
      const customTitle = 'My Favorite Songs'
      const { getByText } = render(
        <TestWrapper>
          <SortableSongList title={customTitle} />
        </TestWrapper>
      )

      expect(getByText(customTitle)).toBeTruthy()
    })

    it('renders empty slots correctly', () => {
      const { getByText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      expect(getByText('Add song to rank 1')).toBeTruthy()
      expect(getByText('Add song to rank 2')).toBeTruthy()
    })

    it('hides buttons when specified', () => {
      const { queryByLabelText } = render(
        <TestWrapper>
          <SortableSongList 
            showAddButton={false}
            showClearButton={false}
            showResetButton={false}
          />
        </TestWrapper>
      )

      expect(queryByLabelText('Add song to rankings')).toBeNull()
      expect(queryByLabelText('Clear all rankings')).toBeNull()
      expect(queryByLabelText('Reset to defaults')).toBeNull()
    })
  })

  describe('Store Integration', () => {
    it('displays songs from store', async () => {
      // Add a song to the store
      act(() => {
        useSortableSongStore.getState().addSongToRanking(mockSong, 0)
      })

      const { getByText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(getByText('Test Song')).toBeTruthy()
        expect(getByText('Test Artist')).toBeTruthy()
      })
    })

    it('handles store errors', async () => {
      act(() => {
        useSortableSongStore.getState().setError('Test error message')
      })

      const { getByText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(getByText('Test error message')).toBeTruthy()
      })
    })

    it('shows loading state', async () => {
      act(() => {
        useSortableSongStore.getState().setLoading(true)
      })

      const { getByText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(getByText('Loading your rankings...')).toBeTruthy()
      })
    })
  })

  describe('User Interactions', () => {
    it('calls onSongAdd when add button is pressed', () => {
      const mockOnSongAdd = jest.fn()
      const { getByLabelText } = render(
        <TestWrapper>
          <SortableSongList onSongAdd={mockOnSongAdd} />
        </TestWrapper>
      )

      fireEvent.press(getByLabelText('Add song to rankings'))
      expect(mockOnSongAdd).toHaveBeenCalledTimes(1)
    })

    it('shows confirmation dialog when clearing all', () => {
      const { Alert } = require('react-native')
      const { getByLabelText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      fireEvent.press(getByLabelText('Clear all rankings'))
      expect(Alert.alert).toHaveBeenCalledWith(
        'Clear All Rankings',
        expect.any(String),
        expect.any(Array)
      )
    })

    it('shows confirmation dialog when resetting', () => {
      const { Alert } = require('react-native')
      const { getByLabelText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      fireEvent.press(getByLabelText('Reset to defaults'))
      expect(Alert.alert).toHaveBeenCalledWith(
        'Reset to Defaults',
        expect.any(String),
        expect.any(Array)
      )
    })

    it('dismisses error when close button is pressed', async () => {
      act(() => {
        useSortableSongStore.getState().setError('Test error')
      })

      const { getByLabelText, queryByText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(queryByText('Test error')).toBeTruthy()
      })

      fireEvent.press(getByLabelText('Dismiss error'))

      await waitFor(() => {
        expect(queryByText('Test error')).toBeNull()
      })
    })
  })

  describe('Accessibility', () => {
    it('has proper accessibility labels', () => {
      const { getByLabelText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      expect(getByLabelText('Song rankings list')).toBeTruthy()
      expect(getByLabelText('Rank 1')).toBeTruthy()
      expect(getByLabelText('Empty ranking slot 1')).toBeTruthy()
    })

    it('handles accessibility mode correctly', async () => {
      const { AccessibilityInfo } = require('react-native')
      AccessibilityInfo.isScreenReaderEnabled.mockResolvedValue(true)

      const { getByLabelText } = render(
        <TestWrapper>
          <SortableSongList />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(getByLabelText('Empty ranking slot 1')).toBeTruthy()
      })
    })
  })
})

describe('Helper Functions', () => {
  describe('convertMusicSongToRankable', () => {
    it('converts MusicSong to RankableSong correctly', () => {
      const result = convertMusicSongToRankable(mockMusicSong)

      expect(result).toEqual({
        id: 'music-song-1',
        artworkURL: 'https://example.com/300x300.jpg',
        trackName: 'Music Song',
        artistName: 'Music Artist',
        audioPreviewURL: 'https://example.com/preview.mp3',
      })
    })

    it('handles missing preview URL', () => {
      const songWithoutPreview = {
        ...mockMusicSong,
        attributes: {
          ...mockMusicSong.attributes,
          previews: [],
        },
      }

      const result = convertMusicSongToRankable(songWithoutPreview)
      expect(result.audioPreviewURL).toBeUndefined()
    })
  })

  describe('useSortableSongActions', () => {
    it('adds song from search successfully', () => {
      const TestComponent = () => {
        const { addSongFromSearch } = useSortableSongActions()
        
        React.useEffect(() => {
          addSongFromSearch(mockMusicSong)
        }, [addSongFromSearch])

        return null
      }

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      )

      const state = useSortableSongStore.getState()
      expect(state.rankings[0]).toMatchObject({
        id: 'music-song-1',
        trackName: 'Music Song',
      })
    })
  })
})
