import React, { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>View } from 'react-native'
import { YStack, XStack, Text, Button, H2, Separator } from 'tamagui'
import SortableSongList, { convertMusicSongToRankable, useSortableSongActions } from './SortableSongList'
import { useSortableSongStore } from '@/stores/useSortableSongStore'
import { RankableSong } from './SortableSongList'

// Demo songs data
const demoSongs: RankableSong[] = [
  {
    id: 'demo-1',
    artworkURL: 'https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=♪',
    trackName: 'Bohemian Rhapsody',
    artistName: 'Queen',
    albumName: 'A Night at the Opera',
    audioPreviewURL: 'https://example.com/preview1.mp3',
  },
  {
    id: 'demo-2',
    artworkURL: 'https://via.placeholder.com/300x300/4ECDC4/FFFFFF?text=♪',
    trackName: 'Imagine',
    artistName: '<PERSON>',
    albumName: 'Imagine',
    audioPreviewURL: 'https://example.com/preview2.mp3',
  },
  {
    id: 'demo-3',
    artworkURL: 'https://via.placeholder.com/300x300/45B7D1/FFFFFF?text=♪',
    trackName: 'Billie Jean',
    artistName: 'Michael Jackson',
    albumName: 'Thriller',
    audioPreviewURL: 'https://example.com/preview3.mp3',
  },
  {
    id: 'demo-4',
    artworkURL: 'https://via.placeholder.com/300x300/F7DC6F/FFFFFF?text=♪',
    trackName: 'Like a Rolling Stone',
    artistName: 'Bob Dylan',
    albumName: 'Highway 61 Revisited',
    audioPreviewURL: 'https://example.com/preview4.mp3',
  },
  {
    id: 'demo-5',
    artworkURL: 'https://via.placeholder.com/300x300/BB8FCE/FFFFFF?text=♪',
    trackName: 'Smells Like Teen Spirit',
    artistName: 'Nirvana',
    albumName: 'Nevermind',
    audioPreviewURL: 'https://example.com/preview5.mp3',
  },
  {
    id: 'demo-6',
    artworkURL: 'https://via.placeholder.com/300x300/85C1E9/FFFFFF?text=♪',
    trackName: 'Hey Jude',
    artistName: 'The Beatles',
    albumName: 'The Beatles 1967-1970',
    audioPreviewURL: 'https://example.com/preview6.mp3',
  },
  {
    id: 'demo-7',
    artworkURL: 'https://via.placeholder.com/300x300/F8C471/FFFFFF?text=♪',
    trackName: "What's Going On",
    artistName: 'Marvin Gaye',
    albumName: "What's Going On",
    audioPreviewURL: 'https://example.com/preview7.mp3',
  },
  {
    id: 'demo-8',
    artworkURL: 'https://via.placeholder.com/300x300/82E0AA/FFFFFF?text=♪',
    trackName: 'Respect',
    artistName: 'Aretha Franklin',
    albumName: 'I Never Loved a Man the Way I Love You',
    audioPreviewURL: 'https://example.com/preview8.mp3',
  },
]

const SortableSongListDemo: React.FC = () => {
  const [selectedSongs, setSelectedSongs] = useState<Set<string>>(new Set())
  const { addSongToRanking, rankings, clearRankings } = useSortableSongStore()

  const handleAddRandomSongs = () => {
    // Add 3-5 random songs to demonstrate the functionality
    const shuffled = [...demoSongs].sort(() => 0.5 - Math.random())
    const songsToAdd = shuffled.slice(0, Math.floor(Math.random() * 3) + 3)
    
    songsToAdd.forEach((song, index) => {
      if (!rankings.some(r => r?.id === song.id)) {
        addSongToRanking(song)
      }
    })
  }

  const handleAddSpecificSong = (song: RankableSong) => {
    const success = addSongToRanking(song)
    if (success) {
      setSelectedSongs(prev => new Set([...prev, song.id]))
    }
  }

  const handleSongAdd = () => {
    // This would typically open a search modal
    // For demo purposes, we'll just add a random song
    const availableSongs = demoSongs.filter(song => 
      !rankings.some(r => r?.id === song.id)
    )
    
    if (availableSongs.length > 0) {
      const randomSong = availableSongs[Math.floor(Math.random() * availableSongs.length)]
      handleAddSpecificSong(randomSong)
    }
  }

  const filledSlots = rankings.filter(song => song !== null).length

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <YStack padding="$4" space="$4">
          {/* Header */}
          <YStack space="$2">
            <H2 textAlign="center">SortableSongList Demo</H2>
            <Text textAlign="center" color="$gray600">
              Drag and drop to reorder • Tap + to add songs • Tap × to remove
            </Text>
          </YStack>

          {/* Stats */}
          <XStack 
            backgroundColor="$gray100" 
            padding="$3" 
            borderRadius="$4" 
            justifyContent="space-between"
          >
            <Text fontWeight="600">Filled Slots: {filledSlots}/10</Text>
            <Text fontWeight="600">
              Available Songs: {demoSongs.length - selectedSongs.size}
            </Text>
          </XStack>

          {/* Demo Controls */}
          <YStack space="$3">
            <Text fontSize="$4" fontWeight="600">Demo Controls</Text>
            <XStack space="$2" flexWrap="wrap">
              <Button
                size="$3"
                onPress={handleAddRandomSongs}
                backgroundColor="$blue500"
                disabled={filledSlots >= 10}
              >
                Add Random Songs
              </Button>
              <Button
                size="$3"
                onPress={clearRankings}
                backgroundColor="$red500"
                disabled={filledSlots === 0}
              >
                Clear All
              </Button>
            </XStack>
          </YStack>

          <Separator />

          {/* Main Component */}
          <SortableSongList
            onSongAdd={handleSongAdd}
            title="My Top 10 Songs"
            showAddButton={true}
            showClearButton={true}
            showResetButton={true}
            maxHeight={600}
          />

          <Separator />

          {/* Available Songs */}
          <YStack space="$3">
            <Text fontSize="$4" fontWeight="600">Available Songs to Add</Text>
            <Text color="$gray600" fontSize="$3">
              Tap any song below to add it to your rankings
            </Text>
            
            <YStack space="$2">
              {demoSongs
                .filter(song => !rankings.some(r => r?.id === song.id))
                .map((song) => (
                  <Button
                    key={song.id}
                    onPress={() => handleAddSpecificSong(song)}
                    backgroundColor="$gray50"
                    borderWidth={1}
                    borderColor="$gray300"
                    borderRadius="$4"
                    padding="$3"
                    justifyContent="flex-start"
                    disabled={filledSlots >= 10}
                  >
                    <XStack alignItems="center" space="$3" flex={1}>
                      <YStack
                        width={40}
                        height={40}
                        backgroundColor="$gray200"
                        borderRadius="$2"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Text fontSize="$2">♪</Text>
                      </YStack>
                      
                      <YStack flex={1} alignItems="flex-start">
                        <Text fontWeight="600" numberOfLines={1}>
                          {song.trackName}
                        </Text>
                        <Text color="$gray600" fontSize="$3" numberOfLines={1}>
                          {song.artistName}
                        </Text>
                        {song.albumName && (
                          <Text color="$gray500" fontSize="$2" numberOfLines={1}>
                            {song.albumName}
                          </Text>
                        )}
                      </YStack>
                      
                      <Text color="$blue600" fontSize="$3">
                        + Add
                      </Text>
                    </XStack>
                  </Button>
                ))}
            </YStack>
            
            {demoSongs.filter(song => !rankings.some(r => r?.id === song.id)).length === 0 && (
              <Text textAlign="center" color="$gray600" fontStyle="italic">
                All songs have been added to your rankings!
              </Text>
            )}
          </YStack>

          {/* Instructions */}
          <YStack 
            backgroundColor="$blue50" 
            padding="$4" 
            borderRadius="$4" 
            borderWidth={1} 
            borderColor="$blue200"
            space="$2"
          >
            <Text fontSize="$4" fontWeight="600" color="$blue900">
              How to Use
            </Text>
            <Text color="$blue800" fontSize="$3">
              • Drag the grip handle (⋮⋮) to reorder songs
            </Text>
            <Text color="$blue800" fontSize="$3">
              • Tap the red × button to remove songs
            </Text>
            <Text color="$blue800" fontSize="$3">
              • Use the + Add Song button or tap songs below to add them
            </Text>
            <Text color="$blue800" fontSize="$3">
              • Rankings are automatically saved and persist between sessions
            </Text>
          </YStack>
        </YStack>
      </ScrollView>
    </SafeAreaView>
  )
}

export default SortableSongListDemo
