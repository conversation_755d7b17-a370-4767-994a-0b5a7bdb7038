import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rollView } from 'react-native'
import { YStack, XStack, Text, Button, Sheet, H3 } from 'tamagui'
import { useRouter } from 'expo-router'
import { Search, Share, Download } from '@tamagui/lucide-icons'
import SortableSongList, { useSortableSongActions } from '@/components/SortableSongList'
import SearchResultsList from '@/components/MusicSearch/SearchResultsList'
import SearchBar from '@/components/MusicSearch/SearchBar'
import { useAppleMusicSearch } from '@/hooks/useSearch'
import { useSortableSongStore } from '@/stores/useSortableSongStore'
import { useQuery } from '@tanstack/react-query'
import { fetchUserBookmarks } from '@/api/bookmarksAPI'

const SongRankingsScreen = () => {
  const router = useRouter()
  const [showSearchSheet, setShowSearchSheet] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const { addSongFromSearch } = useSortableSongActions()
  const { rankings, getFilledSlots } = useSortableSongStore()

  // Search functionality
  const { data: songs = [], isLoading: isSearchLoading, error: searchError } = useAppleMusicSearch({
    searchTerm: searchQuery,
    isEnabled: searchQuery.length > 0 && showSearchSheet
  })

  // Bookmarks functionality
  const { data: bookmarks, isLoading: isBookmarksLoading } = useQuery({
    queryKey: ['userBookmarks'],
    queryFn: fetchUserBookmarks,
    enabled: showSearchSheet
  })

  const handleAddSong = useCallback(() => {
    setShowSearchSheet(true)
  }, [])

  const handleSongSelect = useCallback((song: any) => {
    const success = addSongFromSearch(song)
    if (success) {
      setShowSearchSheet(false)
      setSearchQuery('')
    }
  }, [addSongFromSearch])

  const handleShareRankings = useCallback(async () => {
    const filledRankings = rankings.filter(song => song !== null)
    if (filledRankings.length === 0) {
      return
    }

    const rankingText = filledRankings
      .map((song, index) => `${index + 1}. ${song!.trackName} - ${song!.artistName}`)
      .join('\n')

    try {
      const { Share } = await import('react-native')
      await Share.share({
        message: `My Top ${filledRankings.length} Songs:\n\n${rankingText}\n\nShared from Wullup`,
        title: 'My Song Rankings'
      })
    } catch (error) {
      console.error('Error sharing rankings:', error)
    }
  }, [rankings])

  const handleExportRankings = useCallback(async () => {
    // This could export to various formats or save to device
    const filledRankings = rankings.filter(song => song !== null)
    if (filledRankings.length === 0) {
      return
    }

    try {
      const { FileSystem, StorageAccessFramework } = await import('expo-file-system')
      const rankingData = {
        rankings: filledRankings,
        exportDate: new Date().toISOString(),
        version: '1.0'
      }

      const jsonString = JSON.stringify(rankingData, null, 2)
      const fileName = `song-rankings-${new Date().toISOString().split('T')[0]}.json`

      // For web, this would trigger a download
      // For mobile, this would save to documents
      if (StorageAccessFramework) {
        const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync()
        if (permissions.granted) {
          await StorageAccessFramework.createFileAsync(
            permissions.directoryUri,
            fileName,
            'application/json'
          ).then(uri => {
            return FileSystem.writeAsStringAsync(uri, jsonString)
          })
        }
      }
    } catch (error) {
      console.error('Error exporting rankings:', error)
    }
  }, [rankings])

  const filledSlotsCount = getFilledSlots().length

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <YStack flex={1} backgroundColor="$background">
        {/* Header */}
        <YStack padding="$4" borderBottomWidth={1} borderBottomColor="$borderColor">
          <XStack alignItems="center" justifyContent="space-between" marginBottom="$2">
            <H3>My Song Rankings</H3>
            <XStack space="$2">
              <Button
                size="$3"
                circular
                backgroundColor="$blue500"
                onPress={handleShareRankings}
                disabled={filledSlotsCount === 0}
                accessible={true}
                accessibilityLabel="Share rankings"
              >
                <Share size={16} color="white" />
              </Button>
              <Button
                size="$3"
                circular
                backgroundColor="$green500"
                onPress={handleExportRankings}
                disabled={filledSlotsCount === 0}
                accessible={true}
                accessibilityLabel="Export rankings"
              >
                <Download size={16} color="white" />
              </Button>
            </XStack>
          </XStack>
          
          <Text color="$gray600" fontSize="$3">
            {filledSlotsCount} of 10 slots filled
          </Text>
        </YStack>

        {/* Main Content */}
        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <SortableSongList
            onSongAdd={handleAddSong}
            title="Drag to reorder your favorite songs"
            maxHeight={600}
          />
        </ScrollView>

        {/* Search Sheet */}
        <Sheet
          modal
          open={showSearchSheet}
          onOpenChange={setShowSearchSheet}
          snapPoints={[85]}
          dismissOnSnapToBottom
        >
          <Sheet.Overlay />
          <Sheet.Handle />
          <Sheet.Frame padding="$4" backgroundColor="$background">
            <YStack space="$4" flex={1}>
              <XStack alignItems="center" justifyContent="space-between">
                <H3>Add Song to Rankings</H3>
                <Button
                  size="$3"
                  circular
                  onPress={() => setShowSearchSheet(false)}
                  accessible={true}
                  accessibilityLabel="Close search"
                >
                  ✕
                </Button>
              </XStack>

              <SearchBar
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                onCancel={() => setSearchQuery('')}
                placeholder="Search for songs to add..."
              />

              <ScrollView style={{ flex: 1 }}>
                {searchQuery.length === 0 ? (
                  <YStack space="$3">
                    <Text fontSize="$4" fontWeight="600">Your Bookmarked Songs</Text>
                    {isBookmarksLoading ? (
                      <Text>Loading bookmarks...</Text>
                    ) : bookmarks?.songs?.length > 0 ? (
                      bookmarks.songs.map((song: any) => (
                        <Button
                          key={song.id}
                          onPress={() => handleSongSelect(song)}
                          backgroundColor="$gray100"
                          borderRadius="$4"
                          padding="$3"
                          justifyContent="flex-start"
                        >
                          <XStack alignItems="center" space="$3">
                            <YStack flex={1} alignItems="flex-start">
                              <Text fontWeight="600">{song.trackName}</Text>
                              <Text color="$gray600">{song.artistName}</Text>
                            </YStack>
                          </XStack>
                        </Button>
                      ))
                    ) : (
                      <Text color="$gray600">No bookmarked songs found</Text>
                    )}
                  </YStack>
                ) : (
                  <SearchResultsList
                    songs={songs}
                    isLoading={isSearchLoading}
                    error={searchError}
                    onPostSong={handleSongSelect}
                  />
                )}
              </ScrollView>
            </YStack>
          </Sheet.Frame>
        </Sheet>
      </YStack>
    </SafeAreaView>
  )
}

export default SongRankingsScreen
