import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { RankableSong, RankingSlot } from '@/components/SortableSongList'

export interface SortableSongState {
  // State
  rankings: RankingSlot[]
  isLoading: boolean
  error: string | null
  maxRankings: number
  lastUpdated: Date | null
  
  // Actions
  setRankings: (rankings: RankingSlot[]) => void
  addSongToRanking: (song: RankableSong, position?: number) => boolean
  removeSongFromRanking: (songId: string) => void
  moveSong: (fromIndex: number, toIndex: number) => void
  clearRankings: () => void
  resetToDefaults: () => void
  updateSongInRanking: (songId: string, updatedSong: Partial<RankableSong>) => void
  
  // Utility actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  getEmptySlots: () => number[]
  getFilledSlots: () => number[]
  canAddSong: (songId: string) => boolean
  getSongPosition: (songId: string) => number | null
}

// Default empty rankings (10 slots)
const createEmptyRankings = (count: number = 10): RankingSlot[] => 
  Array(count).fill(null)

// Helper to move an item in an array
const arrayMove = <T,>(array: T[], from: number, to: number): T[] => {
  const newArray = [...array]
  const [item] = newArray.splice(from, 1)
  newArray.splice(to, 0, item)
  return newArray
}

export const useSortableSongStore = create<SortableSongState>()(
  persist(
    (set, get) => ({
      // Initial state
      rankings: createEmptyRankings(10),
      isLoading: false,
      error: null,
      maxRankings: 10,
      lastUpdated: null,

      // Actions
      setRankings: (rankings) => 
        set({ 
          rankings, 
          lastUpdated: new Date(),
          error: null 
        }),

      addSongToRanking: (song, position) => {
        const state = get()
        
        // Check if song already exists
        if (!state.canAddSong(song.id)) {
          set({ error: `Song "${song.trackName}" is already in your rankings` })
          return false
        }

        const newRankings = [...state.rankings]
        
        if (position !== undefined && position >= 0 && position < state.maxRankings) {
          // Insert at specific position, shift others down
          if (newRankings[position] !== null) {
            // Find next empty slot or push existing songs down
            for (let i = position; i < state.maxRankings; i++) {
              if (newRankings[i] === null) {
                newRankings[i] = newRankings[position]
                break
              }
            }
          }
          newRankings[position] = song
        } else {
          // Find first empty slot
          const emptyIndex = newRankings.findIndex(slot => slot === null)
          if (emptyIndex === -1) {
            set({ error: 'All ranking slots are filled. Remove a song first.' })
            return false
          }
          newRankings[emptyIndex] = song
        }

        set({ 
          rankings: newRankings, 
          lastUpdated: new Date(),
          error: null 
        })
        return true
      },

      removeSongFromRanking: (songId) => {
        const state = get()
        const newRankings = state.rankings.map(song => 
          song?.id === songId ? null : song
        )
        
        set({ 
          rankings: newRankings, 
          lastUpdated: new Date(),
          error: null 
        })
      },

      moveSong: (fromIndex, toIndex) => {
        const state = get()
        if (fromIndex === toIndex || 
            fromIndex < 0 || fromIndex >= state.maxRankings ||
            toIndex < 0 || toIndex >= state.maxRankings) {
          return
        }

        const newRankings = arrayMove(state.rankings, fromIndex, toIndex)
        set({ 
          rankings: newRankings, 
          lastUpdated: new Date(),
          error: null 
        })
      },

      clearRankings: () => 
        set({ 
          rankings: createEmptyRankings(get().maxRankings), 
          lastUpdated: new Date(),
          error: null 
        }),

      resetToDefaults: () => 
        set({ 
          rankings: createEmptyRankings(10),
          maxRankings: 10,
          isLoading: false,
          error: null,
          lastUpdated: new Date()
        }),

      updateSongInRanking: (songId, updatedSong) => {
        const state = get()
        const newRankings = state.rankings.map(song => 
          song?.id === songId ? { ...song, ...updatedSong } : song
        )
        
        set({ 
          rankings: newRankings, 
          lastUpdated: new Date(),
          error: null 
        })
      },

      // Utility actions
      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error }),

      getEmptySlots: () => {
        const state = get()
        return state.rankings
          .map((slot, index) => slot === null ? index : -1)
          .filter(index => index !== -1)
      },

      getFilledSlots: () => {
        const state = get()
        return state.rankings
          .map((slot, index) => slot !== null ? index : -1)
          .filter(index => index !== -1)
      },

      canAddSong: (songId) => {
        const state = get()
        return !state.rankings.some(song => song?.id === songId)
      },

      getSongPosition: (songId) => {
        const state = get()
        const position = state.rankings.findIndex(song => song?.id === songId)
        return position === -1 ? null : position
      },
    }),
    {
      name: 'sortable-song-rankings',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        rankings: state.rankings,
        maxRankings: state.maxRankings,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
)
